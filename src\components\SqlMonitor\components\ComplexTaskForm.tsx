import React, { useState, useEffect } from 'react';
import { Form, Input, Select, Button, Card, Row, Col, message, Tabs, InputNumber, TimePicker } from 'antd';
import dayjs from 'dayjs';
import type { TabsProps } from 'antd';

// 导入重构后的模块
import type { 
  TaskBasic, 
  TaskAlert, 
  DBConnection, 
  AlertSend, 
  OtherInfo} from '../types';
import { 
  WEEKDAY_OPTIONS,
  FREQUENCY_UNIT_OPTIONS,
  RETRY_FREQUENCY_UNIT_OPTIONS,
  DEFAULT_RETRY_NUM,
  DEFAULT_FREQUENCY,
  DEFAULT_RETRY_FREQUENCY,
  FORM_PLACEHOLDERS,
  FORM_BUTTON_TEXT
} from '../constants';
import { TaskService } from '../services';
import { useTaskForm } from '../hooks';
import { formStyles } from '../styles';
import { formatFrequencyToString } from '../../../utils/frequencyConverter';
import { TaskGroupSelect } from './common/TaskGroupSelect';

const { Option } = Select;

interface ComplexTaskFormProps {
  initialData?: TaskBasic;
  onSubmit?: () => void;
  onCancel?: () => void;
  onReset?: () => void;
  isEdit?: boolean;
}

/**
 * 复合任务表单组件
 * 支持多标签页展示不同类型的配置
 */
const ComplexTaskForm: React.FC<ComplexTaskFormProps> = ({ 
  initialData, 
  onSubmit, 
  onCancel, 
  onReset 
}) => {
  const [activeTab, setActiveTab] = useState('basic');
  const [submitLoading, setSubmitLoading] = useState(false);

  // 使用表单管理hook
  const {
    form,
    formState,
    isEditMode,
    handleSubmit,
    handleReset,
    setFormData,
    getFormData,
    validateForm,
  } = useTaskForm({
    action: initialData ? 'edit' : 'add',
    initialData,
    onSuccess: (_data) => {
      message.success(isEditMode ? '更新任务成功' : '创建任务成功');
      onSubmit?.();
    },
    onError: (error) => {
      message.error(isEditMode ? '更新任务失败' : '创建任务失败');
      console.error('表单提交失败:', error);
    },
  });

  // 各种数据状态
  const [alerts, setAlerts] = useState<TaskAlert[]>([]);
  const [alertSends, setAlertSends] = useState<AlertSend[]>([]);
  const [dbConnection, setDbConnection] = useState<DBConnection | null>(null);
  const [otherInfo, setOtherInfo] = useState<OtherInfo | null>(null);

  // 可选择的数据
  const [availableAlerts, setAvailableAlerts] = useState<TaskAlert[]>([]);
  const [availableDbConnections, setAvailableDbConnections] = useState<DBConnection[]>([]);
  const [availableAlertSends, setAvailableAlertSends] = useState<AlertSend[]>([]);
  const [availableOtherInfos, setAvailableOtherInfos] = useState<OtherInfo[]>([]);

  // Modal状态
  const [alertModal, setAlertModal] = useState({
    visible: false,
    editingIndex: -1,
  });

  const [dbConnectionModal, setDbConnectionModal] = useState({
    visible: false,
  });

  const [alertSendModal, setAlertSendModal] = useState({
    visible: false,
    editingIndex: -1,
  });

  const [otherInfoModal, setOtherInfoModal] = useState({
    visible: false,
  });

  const [selectModal, setSelectModal] = useState({
    visible: false,
    type: '' as 'alert' | 'alertSend' | 'dbConnection' | 'otherInfo',
    multiple: false,
  });

  // 初始化表单数据
  useEffect(() => {
    if (initialData) {
      // 处理初始数据，转换为表单格式
      const formData = {
        ...initialData,
        weekday: initialData.weekday || [],
        frequency_value: initialData.frequency?.value || DEFAULT_FREQUENCY.value,
        frequency_unit: initialData.frequency?.unit || DEFAULT_FREQUENCY.unit,
        retry_frequency_value: initialData.retry_frequency?.value || DEFAULT_RETRY_FREQUENCY.value,
        retry_frequency_unit: initialData.retry_frequency?.unit || DEFAULT_RETRY_FREQUENCY.unit,
        start_time: initialData.start_time ? dayjs(initialData.start_time, 'HH:mm:ss') : null,
        end_time: initialData.end_time ? dayjs(initialData.end_time, 'HH:mm:ss') : null,
      };
      setFormData(formData);
    }
  }, [initialData, setFormData]);

  // 加载可选数据
  useEffect(() => {
    const loadAvailableData = async () => {
      try {
        const [alertsRes, dbConnectionsRes, alertSendsRes, otherInfosRes] = await Promise.all([
          TaskService.getAlerts(),
          TaskService.getDbConnections(),
          TaskService.getAlertSends(),
          TaskService.getOtherInfos(),
        ]);

        setAvailableAlerts(alertsRes);
        setAvailableDbConnections(dbConnectionsRes);
        setAvailableAlertSends(alertSendsRes);
        setAvailableOtherInfos(otherInfosRes);
      } catch (error) {
        console.error('加载可选数据失败:', error);
        message.error('加载数据失败');
      }
    };

    loadAvailableData();
  }, []);

  // 表单提交处理
  const handleFormSubmit = async () => {
    const isValid = await validateForm();
    if (!isValid) {
      message.error('请检查表单数据');
      return;
    }

    setSubmitLoading(true);
    try {
      const formData = getFormData();
      
      // 转换表单数据为提交格式
      const submitData = {
        ...formData,
        weekday: Array.isArray(formData.weekday) ? formData.weekday.join(',') : formData.weekday,
        frequency: formatFrequencyToString(formData.frequency_value, formData.frequency_unit),
        retry_frequency: formatFrequencyToString(formData.retry_frequency_value, formData.retry_frequency_unit),
        start_time: formData.start_time ? formData.start_time.format('HH:mm:ss') : '',
        end_time: formData.end_time ? formData.end_time.format('HH:mm:ss') : '',
        alert_task_id: alerts.map(alert => `alert_${alert.id}`).join(','),
        alert_send_id: alertSends.map(send => `send_${send.id}`).join(','),
        db_connection_id: dbConnection ? `db_${dbConnection.id}` : '',
        other_info_id: otherInfo ? `info_${otherInfo.id}` : '',
      };

      await handleSubmit(submitData);
    } catch (error) {
      console.error('表单提交失败:', error);
    } finally {
      setSubmitLoading(false);
    }
  };

  // 标签页配置
  const tabItems: TabsProps['items'] = [
    {
      key: 'basic',
      label: '基本信息',
      children: (
        <Card title="基本配置" size="small">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="任务名称"
                name="name"
                rules={[{ required: true, message: '请输入任务名称' }]}
              >
                <Input placeholder={FORM_PLACEHOLDERS.name} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="任务分组"
                name="group_name"
                rules={[{ required: true, message: '请选择任务分组' }]}
              >
                <TaskGroupSelect 
                  placeholder={FORM_PLACEHOLDERS.group} 
                  allowClear 
                  dynamicSearch={true} 
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="开始时间"
                name="start_time"
                rules={[{ required: true, message: '请选择开始时间' }]}
              >
                <TimePicker 
                  placeholder={FORM_PLACEHOLDERS.startTime} 
                  format="HH:mm:ss" 
                  className="w-full" 
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="结束时间"
                name="end_time"
                rules={[{ required: true, message: '请选择结束时间' }]}
              >
                <TimePicker 
                  placeholder={FORM_PLACEHOLDERS.endTime} 
                  format="HH:mm:ss" 
                  className="w-full" 
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="执行星期"
                name="weekday"
                rules={[{ required: true, message: '请选择执行星期' }]}
              >
                <Select
                  mode="multiple"
                  placeholder={FORM_PLACEHOLDERS.weekday}
                  allowClear
                >
                  {WEEKDAY_OPTIONS.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="执行频率">
                <Input.Group compact>
                  <Form.Item
                    name="frequency_value"
                    noStyle
                    rules={[{ required: true, message: '请输入频率值' }]}
                  >
                    <InputNumber
                      placeholder="频率值"
                      min={1}
                      style={{ width: '60%' }}
                    />
                  </Form.Item>
                  <Form.Item
                    name="frequency_unit"
                    noStyle
                    rules={[{ required: true, message: '请选择频率单位' }]}
                  >
                    <Select placeholder="单位" style={{ width: '40%' }}>
                      {FREQUENCY_UNIT_OPTIONS.map(option => (
                        <Option key={option.value} value={option.value}>
                          {option.label}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Input.Group>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="重试次数"
                name="retry_num"
                initialValue={DEFAULT_RETRY_NUM}
              >
                <InputNumber 
                  placeholder={FORM_PLACEHOLDERS.retryNum} 
                  min={0} 
                  className="w-full" 
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="重试间隔">
                <Input.Group compact>
                  <Form.Item
                    name="retry_frequency_value"
                    noStyle
                    initialValue={DEFAULT_RETRY_FREQUENCY.value}
                  >
                    <InputNumber
                      placeholder="间隔值"
                      min={1}
                      style={{ width: '60%' }}
                    />
                  </Form.Item>
                  <Form.Item
                    name="retry_frequency_unit"
                    noStyle
                    initialValue={DEFAULT_RETRY_FREQUENCY.unit}
                  >
                    <Select placeholder="单位" style={{ width: '40%' }}>
                      {RETRY_FREQUENCY_UNIT_OPTIONS.map(option => (
                        <Option key={option.value} value={option.value}>
                          {option.label}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Input.Group>
              </Form.Item>
            </Col>
          </Row>
        </Card>
      ),
    },
    // 其他标签页可以根据需要添加
  ];

  return (
    <div className="h-full flex flex-col">
      <Form
        form={form}
        layout="vertical"
        className="flex-1 overflow-auto"
      >
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          className="h-full"
        />
      </Form>

      {/* 底部操作栏 */}
      <div className={formStyles.footerContainer}>
        <div className="flex justify-between items-center">
          <div className={formStyles.footerHint}>
            {isEditMode ? '编辑任务信息' : '创建新任务'}
          </div>
          <div className={formStyles.buttonGroup}>
            <Button
              onClick={onCancel}
              className={`${formStyles.actionButton} ${formStyles.cancelButton}`}
            >
              {FORM_BUTTON_TEXT.cancel}
            </Button>
            <Button
              onClick={() => {
                handleReset();
                onReset?.();
              }}
              className={`${formStyles.actionButton} ${formStyles.resetButton}`}
            >
              {FORM_BUTTON_TEXT.reset}
            </Button>
            <Button
              type="primary"
              loading={submitLoading || formState === 'submitting'}
              onClick={handleFormSubmit}
              className={`${formStyles.actionButton} ${isEditMode ? formStyles.confirmButton : formStyles.submitButton}`}
            >
              {isEditMode ? FORM_BUTTON_TEXT.update : FORM_BUTTON_TEXT.submit}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComplexTaskForm;
