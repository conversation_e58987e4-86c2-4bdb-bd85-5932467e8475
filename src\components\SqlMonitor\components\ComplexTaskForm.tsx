import React, { useEffect, useState } from 'react';
import { Form, Input, Select, Button, Card, Row, Col, message, Tabs, InputNumber, TimePicker, Space } from 'antd';
import { SettingOutlined, AlertOutlined, DatabaseOutlined, NotificationOutlined, InfoCircleOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import type { TabsProps } from 'antd';

// 导入重构后的模块
import type { TaskBasic } from '../types';
import {
  WEEKDAY_OPTIONS,
  FREQUENCY_UNIT_OPTIONS,
  RETRY_FREQUENCY_UNIT_OPTIONS,
  DEFAULT_RETRY_NUM,
  DEFAULT_FREQUENCY,
  DEFAULT_RETRY_FREQUENCY,
  FORM_PLACEHOLDERS,
  FORM_BUTTON_TEXT,
} from '../constants';
import { useTaskForm } from '../hooks';
import { formStyles } from '../styles';
import { formatFrequencyToString } from '../../../utils/frequencyConverter';
import { TaskGroupSelect } from './common/TaskGroupSelect';

const { Option } = Select;

interface ComplexTaskFormProps {
  initialData?: TaskBasic;
  onSubmit?: () => void;
  onCancel?: () => void;
  onReset?: () => void;
}

/**
 * 复合任务表单组件
 * 支持多标签页展示不同类型的配置
 */
const ComplexTaskForm: React.FC<ComplexTaskFormProps> = ({ initialData, onSubmit, onCancel, onReset }) => {
  const [activeTab, setActiveTab] = useState('basic');
  const [submitLoading, setSubmitLoading] = useState(false);

  // 使用表单管理hook
  const { form, formState, isEditMode, handleSubmit, handleReset, getFormData, validateForm } = useTaskForm({
    action: initialData ? 'edit' : 'add',
    initialData,
    onSuccess: () => {
      message.success(isEditMode ? '更新任务成功' : '创建任务成功');
      onSubmit?.();
    },
    onError: () => {
      message.error(isEditMode ? '更新任务失败' : '创建任务失败');
    },
  });

  // 初始化表单数据
  useEffect(() => {
    if (initialData) {
      // 处理初始数据，转换为表单格式
      const formData = {
        name: initialData.name,
        group_name: initialData.group_name,
        status: initialData.status,
        weekday: initialData.weekday || [],
        frequency_value: initialData.frequency?.value || DEFAULT_FREQUENCY.value,
        frequency_unit: initialData.frequency?.unit || DEFAULT_FREQUENCY.unit,
        retry_frequency_value: initialData.retry_frequency?.value || DEFAULT_RETRY_FREQUENCY.value,
        retry_frequency_unit: initialData.retry_frequency?.unit || DEFAULT_RETRY_FREQUENCY.unit,
        retry_num: initialData.retry_num,
        start_time: initialData.start_time ? dayjs(initialData.start_time, 'HH:mm:ss') : undefined,
        end_time: initialData.end_time ? dayjs(initialData.end_time, 'HH:mm:ss') : undefined,
      };
      form.setFieldsValue(formData);
    }
  }, [initialData, form]);

  // 表单提交处理
  const handleFormSubmit = async () => {
    const isValid = await validateForm();
    if (!isValid) {
      message.error('请检查表单数据');
      return;
    }

    setSubmitLoading(true);
    try {
      const formData = getFormData();

      // 转换表单数据为提交格式
      const submitData = {
        ...formData,
        weekday: Array.isArray(formData.weekday) ? formData.weekday.join(',') : formData.weekday,
        frequency: formatFrequencyToString(formData.frequency_value, formData.frequency_unit),
        retry_frequency: formatFrequencyToString(formData.retry_frequency_value, formData.retry_frequency_unit),
        start_time: formData.start_time ? formData.start_time.format('HH:mm:ss') : '',
        end_time: formData.end_time ? formData.end_time.format('HH:mm:ss') : '',
        alert_task_id: '',
        alert_send_id: '',
        db_connection_id: '',
        other_info_id: '',
      };

      await handleSubmit(submitData);
    } catch (error) {
      console.error('表单提交失败:', error);
    } finally {
      setSubmitLoading(false);
    }
  };

  // 标签页配置
  const tabItems: TabsProps['items'] = [
    {
      key: 'basic',
      label: (
        <span>
          <SettingOutlined />
          <span style={{ marginLeft: 8 }}>基本信息</span>
        </span>
      ),
      children: (
        <div className={formStyles.tabContent}>
          <Card title='基本配置' size='small' className='mb-4'>
            <Row gutter={16} className='mb-4'>
              <Col span={12}>
                <Form.Item label='任务名称' name='name' rules={[{ required: true, message: '请输入任务名称' }]}>
                  <Input placeholder={FORM_PLACEHOLDERS.name} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label='任务分组' name='group_name' rules={[{ required: true, message: '请选择任务分组' }]}>
                  <TaskGroupSelect placeholder={FORM_PLACEHOLDERS.group} allowClear dynamicSearch={true} />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16} className='mb-4'>
              <Col span={8}>
                <Form.Item label='开始时间' name='start_time' rules={[{ required: true, message: '请选择开始时间' }]}>
                  <TimePicker placeholder={FORM_PLACEHOLDERS.startTime} format='HH:mm:ss' className='w-full' />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label='结束时间' name='end_time' rules={[{ required: true, message: '请选择结束时间' }]}>
                  <TimePicker placeholder={FORM_PLACEHOLDERS.endTime} format='HH:mm:ss' className='w-full' />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label='执行星期' name='weekday' rules={[{ required: true, message: '请选择执行星期' }]}>
                  <Select mode='multiple' placeholder={FORM_PLACEHOLDERS.weekday} allowClear>
                    {WEEKDAY_OPTIONS.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16} className='mb-4'>
              <Col span={8}>
                <Form.Item label='执行频率'>
                  <Space.Compact style={{ width: '100%' }}>
                    <Form.Item name='frequency_value' noStyle rules={[{ required: true, message: '请输入频率值' }]}>
                      <InputNumber placeholder='频率值' min={1} style={{ width: '60%' }} />
                    </Form.Item>
                    <Form.Item name='frequency_unit' noStyle rules={[{ required: true, message: '请选择频率单位' }]}>
                      <Select placeholder='单位' style={{ width: '40%' }}>
                        {FREQUENCY_UNIT_OPTIONS.map(option => (
                          <Option key={option.value} value={option.value}>
                            {option.label}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Space.Compact>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label='重试次数' name='retry_num' initialValue={DEFAULT_RETRY_NUM}>
                  <InputNumber placeholder={FORM_PLACEHOLDERS.retryNum} min={0} className='w-full' />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label='重试间隔'>
                  <Space.Compact style={{ width: '100%' }}>
                    <Form.Item name='retry_frequency_value' noStyle initialValue={DEFAULT_RETRY_FREQUENCY.value}>
                      <InputNumber placeholder='间隔值' min={1} style={{ width: '60%' }} />
                    </Form.Item>
                    <Form.Item name='retry_frequency_unit' noStyle initialValue={DEFAULT_RETRY_FREQUENCY.unit}>
                      <Select placeholder='单位' style={{ width: '40%' }}>
                        {RETRY_FREQUENCY_UNIT_OPTIONS.map(option => (
                          <Option key={option.value} value={option.value}>
                            {option.label}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Space.Compact>
                </Form.Item>
              </Col>
            </Row>
          </Card>
        </div>
      ),
    },
    {
      key: 'alert',
      label: (
        <span>
          <AlertOutlined />
          <span style={{ marginLeft: 8 }}>告警配置</span>
        </span>
      ),
      children: (
        <div className={formStyles.tabContent}>
          <Card title='告警触发条件' size='small' className='mb-4'>
            <div className={formStyles.placeholderContent}>
              <p>告警配置功能开发中...</p>
              <p className='text-sm'>此处将配置SQL执行结果的告警条件</p>
            </div>
          </Card>
        </div>
      ),
    },
    {
      key: 'database',
      label: (
        <span>
          <DatabaseOutlined />
          <span style={{ marginLeft: 8 }}>数据库连接</span>
        </span>
      ),
      children: (
        <div className={formStyles.tabContent}>
          <Card title='数据库连接配置' size='small' className='mb-4'>
            <div className={formStyles.placeholderContent}>
              <p>数据库连接配置功能开发中...</p>
              <p className='text-sm'>此处将配置MySQL/Oracle数据库连接信息</p>
            </div>
          </Card>
        </div>
      ),
    },
    {
      key: 'notification',
      label: (
        <span>
          <NotificationOutlined />
          <span style={{ marginLeft: 8 }}>告警发送</span>
        </span>
      ),
      children: (
        <div className={formStyles.tabContent}>
          <Card title='告警发送配置' size='small' className='mb-4'>
            <div className={formStyles.placeholderContent}>
              <p>告警发送配置功能开发中...</p>
              <p className='text-sm'>此处将配置Kafka/Prometheus告警发送方式</p>
            </div>
          </Card>
        </div>
      ),
    },
    {
      key: 'other',
      label: (
        <span>
          <InfoCircleOutlined />
          <span style={{ marginLeft: 8 }}>其他信息</span>
        </span>
      ),
      children: (
        <div className={formStyles.tabContent}>
          <Card title='附加信息配置' size='small' className='mb-4'>
            <div className={formStyles.placeholderContent}>
              <p>附加信息配置功能开发中...</p>
              <p className='text-sm'>此处将配置业务系统、主机等附加信息</p>
            </div>
          </Card>
        </div>
      ),
    },
  ];

  return (
    <div className='h-full flex flex-col'>
      <Form form={form} layout='vertical' className='flex-1 overflow-hidden'>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          className={`${formStyles.formTabs} h-full`}
          style={{ height: '100%' }}
          tabBarStyle={{
            marginBottom: 0,
            paddingLeft: 16,
            paddingRight: 16,
            borderBottom: '1px solid #f0f0f0',
            background: '#fafafa',
          }}
        />
      </Form>

      {/* 底部操作栏 */}
      <div className={formStyles.footerContainer}>
        <div className='flex justify-between items-center'>
          <div className={formStyles.footerHint}>{isEditMode ? '编辑任务信息' : '创建新任务'}</div>
          <div className={formStyles.buttonGroup}>
            <Button onClick={onCancel} className={`${formStyles.actionButton} ${formStyles.cancelButton}`}>
              {FORM_BUTTON_TEXT.cancel}
            </Button>
            <Button
              onClick={() => {
                handleReset();
                onReset?.();
              }}
              className={`${formStyles.actionButton} ${formStyles.resetButton}`}
            >
              {FORM_BUTTON_TEXT.reset}
            </Button>
            <Button
              type='primary'
              loading={submitLoading || formState === 'submitting'}
              onClick={handleFormSubmit}
              className={`${formStyles.actionButton} ${isEditMode ? formStyles.confirmButton : formStyles.submitButton}`}
            >
              {isEditMode ? FORM_BUTTON_TEXT.update : FORM_BUTTON_TEXT.submit}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComplexTaskForm;
